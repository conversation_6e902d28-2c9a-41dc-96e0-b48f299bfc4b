#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Advanced Interactive Dashboard for Fawterha
Real-time analytics, charts, and business intelligence
"""

import sys
from datetime import datetime, timedelta
from typing import Dict, List, Any
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, 
    QPushButton, QFrame, QScrollArea, QComboBox, QDateEdit,
    QProgressBar, QTabWidget, QTableWidget, QTableWidgetItem,
    QHeaderView, QSizePolicy
)
from PySide6.QtCore import Qt, QTimer, QDate, Signal, QThread, QPropertyAnimation
from PySide6.QtGui import QFont, QPixmap, QPainter, QColor, QBrush, QPen
from PySide6.QtCharts import QChart, QChartView, QLineSeries, QBarSeries, QBarSet, QPieSeries, QValueAxis, QBarCategoryAxis
from utils.translation_manager import tr


class MetricCard(QFrame):
    """Individual metric display card."""
    
    def __init__(self, title: str, value: str, change: str = "", trend: str = "neutral"):
        super().__init__()
        self.setup_ui(title, value, change, trend)
        self.setup_animations()
    
    def setup_ui(self, title: str, value: str, change: str, trend: str):
        """Setup the metric card UI."""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 10px;
            }
            QFrame:hover {
                border-color: #2196f3;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }
        """)
        self.setFixedHeight(120)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 10, 15, 10)
        
        # Title
        title_label = QLabel(title)
        title_font = QFont()
        title_font.setPointSize(10)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #666666;")
        layout.addWidget(title_label)
        
        # Value
        value_label = QLabel(value)
        value_font = QFont()
        value_font.setPointSize(24)
        value_font.setBold(True)
        value_label.setFont(value_font)
        value_label.setStyleSheet("color: #333333;")
        layout.addWidget(value_label)
        
        # Change indicator
        if change:
            change_label = QLabel(change)
            change_font = QFont()
            change_font.setPointSize(9)
            change_label.setFont(change_font)
            
            if trend == "up":
                change_label.setStyleSheet("color: #4caf50;")
            elif trend == "down":
                change_label.setStyleSheet("color: #f44336;")
            else:
                change_label.setStyleSheet("color: #666666;")
            
            layout.addWidget(change_label)
        
        layout.addStretch()
    
    def setup_animations(self):
        """Setup hover animations."""
        self.animation = QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(200)
    
    def update_value(self, new_value: str, new_change: str = "", new_trend: str = "neutral"):
        """Update the metric card values."""
        # Find and update the value label
        for i in range(self.layout().count()):
            widget = self.layout().itemAt(i).widget()
            if isinstance(widget, QLabel) and widget.font().pointSize() == 24:
                widget.setText(new_value)
                break


class ChartWidget(QFrame):
    """Chart container widget."""
    
    def __init__(self, title: str, chart_type: str = "line"):
        super().__init__()
        self.title = title
        self.chart_type = chart_type
        self.setup_ui()
        self.create_chart()
    
    def setup_ui(self):
        """Setup the chart widget UI."""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Title
        title_label = QLabel(self.title)
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # Chart view will be added in create_chart()
        
    def create_chart(self):
        """Create the chart based on type."""
        self.chart = QChart()
        self.chart.setTheme(QChart.ChartThemeLight)
        self.chart.setAnimationOptions(QChart.SeriesAnimations)
        
        self.chart_view = QChartView(self.chart)
        self.chart_view.setRenderHint(QPainter.Antialiasing)
        
        self.layout().addWidget(self.chart_view)
    
    def update_data(self, data: List[Any]):
        """Update chart data."""
        # Clear existing series
        self.chart.removeAllSeries()
        
        if self.chart_type == "line":
            self._create_line_chart(data)
        elif self.chart_type == "bar":
            self._create_bar_chart(data)
        elif self.chart_type == "pie":
            self._create_pie_chart(data)
    
    def _create_line_chart(self, data: List[tuple]):
        """Create line chart."""
        series = QLineSeries()
        
        for x, y in data:
            series.append(x, y)
        
        self.chart.addSeries(series)
        self.chart.createDefaultAxes()
    
    def _create_bar_chart(self, data: List[tuple]):
        """Create bar chart."""
        series = QBarSeries()
        bar_set = QBarSet("البيانات")
        
        categories = []
        for label, value in data:
            bar_set.append(value)
            categories.append(label)
        
        series.append(bar_set)
        self.chart.addSeries(series)
        
        # Setup axes
        axis_x = QBarCategoryAxis()
        axis_x.append(categories)
        self.chart.addAxis(axis_x, Qt.AlignBottom)
        series.attachAxis(axis_x)
        
        axis_y = QValueAxis()
        self.chart.addAxis(axis_y, Qt.AlignLeft)
        series.attachAxis(axis_y)
    
    def _create_pie_chart(self, data: List[tuple]):
        """Create pie chart."""
        series = QPieSeries()
        
        for label, value in data:
            series.append(label, value)
        
        self.chart.addSeries(series)


class RecentActivityWidget(QFrame):
    """Recent activity display widget."""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the recent activity UI."""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 10, 15, 10)
        
        # Title
        title_label = QLabel(tr("dashboard.recent_activity", "النشاط الأخير"))
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # Activity list
        self.activity_table = QTableWidget()
        self.activity_table.setColumnCount(3)
        self.activity_table.setHorizontalHeaderLabels([
            tr("common.time", "الوقت"),
            tr("common.description", "الوصف"),
            tr("common.amount", "المبلغ")
        ])
        
        # Configure table
        header = self.activity_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        
        self.activity_table.setAlternatingRowColors(True)
        self.activity_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.activity_table.verticalHeader().setVisible(False)
        
        layout.addWidget(self.activity_table)
    
    def update_activities(self, activities: List[Dict]):
        """Update the activity list."""
        self.activity_table.setRowCount(len(activities))
        
        for row, activity in enumerate(activities):
            # Time
            time_item = QTableWidgetItem(activity.get('time', ''))
            self.activity_table.setItem(row, 0, time_item)
            
            # Description
            desc_item = QTableWidgetItem(activity.get('description', ''))
            self.activity_table.setItem(row, 1, desc_item)
            
            # Amount
            amount_item = QTableWidgetItem(activity.get('amount', ''))
            self.activity_table.setItem(row, 2, amount_item)


class AdvancedDashboard(QWidget):
    """Advanced interactive dashboard."""
    
    # Signals
    data_refresh_requested = Signal()
    
    def __init__(self, db_manager, currency_manager=None):
        super().__init__()
        self.db_manager = db_manager
        self.currency_manager = currency_manager
        self.setup_ui()
        self.setup_refresh_timer()
        self.load_dashboard_data()
    
    def setup_ui(self):
        """Setup the dashboard UI."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # Header
        header_layout = QHBoxLayout()
        
        title_label = QLabel(tr("dashboard.title", "لوحة التحكم"))
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Refresh button
        refresh_btn = QPushButton(tr("common.refresh", "تحديث"))
        refresh_btn.clicked.connect(self.load_dashboard_data)
        header_layout.addWidget(refresh_btn)
        
        # Date range selector
        date_combo = QComboBox()
        date_combo.addItems([
            tr("dashboard.today", "اليوم"),
            tr("dashboard.this_week", "هذا الأسبوع"),
            tr("dashboard.this_month", "هذا الشهر"),
            tr("dashboard.this_year", "هذا العام")
        ])
        date_combo.currentTextChanged.connect(self.on_date_range_changed)
        header_layout.addWidget(date_combo)
        
        layout.addLayout(header_layout)
        
        # Metrics cards
        self.create_metrics_section(layout)
        
        # Charts section
        self.create_charts_section(layout)
        
        # Recent activity
        self.create_activity_section(layout)
    
    def create_metrics_section(self, parent_layout):
        """Create the metrics cards section."""
        metrics_layout = QHBoxLayout()
        
        # Sales metric
        self.sales_card = MetricCard(
            tr("dashboard.total_sales", "إجمالي المبيعات"),
            "0",
            "",
            "neutral"
        )
        metrics_layout.addWidget(self.sales_card)
        
        # Invoices metric
        self.invoices_card = MetricCard(
            tr("dashboard.total_invoices", "إجمالي الفواتير"),
            "0",
            "",
            "neutral"
        )
        metrics_layout.addWidget(self.invoices_card)
        
        # Customers metric
        self.customers_card = MetricCard(
            tr("dashboard.total_customers", "إجمالي العملاء"),
            "0",
            "",
            "neutral"
        )
        metrics_layout.addWidget(self.customers_card)
        
        # Products metric
        self.products_card = MetricCard(
            tr("dashboard.total_products", "إجمالي المنتجات"),
            "0",
            "",
            "neutral"
        )
        metrics_layout.addWidget(self.products_card)
        
        parent_layout.addLayout(metrics_layout)
    
    def create_charts_section(self, parent_layout):
        """Create the charts section."""
        charts_layout = QHBoxLayout()
        
        # Sales chart
        self.sales_chart = ChartWidget(
            tr("dashboard.sales_trend", "اتجاه المبيعات"),
            "line"
        )
        charts_layout.addWidget(self.sales_chart)
        
        # Top products chart
        self.products_chart = ChartWidget(
            tr("dashboard.top_products", "أفضل المنتجات"),
            "bar"
        )
        charts_layout.addWidget(self.products_chart)
        
        parent_layout.addLayout(charts_layout)
    
    def create_activity_section(self, parent_layout):
        """Create the recent activity section."""
        self.activity_widget = RecentActivityWidget()
        parent_layout.addWidget(self.activity_widget)
    
    def setup_refresh_timer(self):
        """Setup automatic refresh timer."""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.load_dashboard_data)
        self.refresh_timer.start(300000)  # Refresh every 5 minutes
    
    def load_dashboard_data(self):
        """Load all dashboard data."""
        try:
            self.load_metrics()
            self.load_charts_data()
            self.load_recent_activity()
        except Exception as e:
            print(f"Error loading dashboard data: {e}")
    
    def load_metrics(self):
        """Load metrics data."""
        try:
            # Total sales
            sales_query = """
            SELECT COALESCE(SUM(total), 0) as total_sales
            FROM invoices 
            WHERE status = 'paid'
            """
            sales_result = self.db_manager.execute_query(sales_query)
            total_sales = sales_result[0]['total_sales'] if sales_result else 0
            
            # Format currency
            currency_symbol = "ر.س"
            if self.currency_manager:
                primary_currency = self.currency_manager.get_primary_currency()
                if primary_currency:
                    currency_symbol = primary_currency.symbol
            
            self.sales_card.update_value(f"{total_sales:,.0f} {currency_symbol}")
            
            # Total invoices
            invoices_query = "SELECT COUNT(*) as count FROM invoices"
            invoices_result = self.db_manager.execute_query(invoices_query)
            total_invoices = invoices_result[0]['count'] if invoices_result else 0
            self.invoices_card.update_value(str(total_invoices))
            
            # Total customers
            customers_query = "SELECT COUNT(*) as count FROM customers"
            customers_result = self.db_manager.execute_query(customers_query)
            total_customers = customers_result[0]['count'] if customers_result else 0
            self.customers_card.update_value(str(total_customers))
            
            # Total products
            products_query = "SELECT COUNT(*) as count FROM products"
            products_result = self.db_manager.execute_query(products_query)
            total_products = products_result[0]['count'] if products_result else 0
            self.products_card.update_value(str(total_products))
            
        except Exception as e:
            print(f"Error loading metrics: {e}")
    
    def load_charts_data(self):
        """Load charts data."""
        try:
            # Sales trend data (last 7 days)
            sales_trend_query = """
            SELECT 
                DATE(issue_date) as date,
                COALESCE(SUM(total), 0) as daily_sales
            FROM invoices 
            WHERE status = 'paid' 
            AND issue_date >= date('now', '-7 days')
            GROUP BY DATE(issue_date)
            ORDER BY date
            """
            sales_trend_data = self.db_manager.execute_query(sales_trend_query)
            
            # Convert to chart format
            chart_data = [(i, row['daily_sales']) for i, row in enumerate(sales_trend_data)]
            self.sales_chart.update_data(chart_data)
            
            # Top products data
            top_products_query = """
            SELECT 
                p.name,
                COALESCE(SUM(ii.quantity), 0) as total_sold
            FROM products p
            LEFT JOIN invoice_items ii ON p.id = ii.product_id
            LEFT JOIN invoices i ON ii.invoice_id = i.id
            WHERE i.status = 'paid' OR i.status IS NULL
            GROUP BY p.id, p.name
            ORDER BY total_sold DESC
            LIMIT 5
            """
            top_products_data = self.db_manager.execute_query(top_products_query)
            
            # Convert to chart format
            products_chart_data = [(row['name'], row['total_sold']) for row in top_products_data]
            self.products_chart.update_data(products_chart_data)
            
        except Exception as e:
            print(f"Error loading charts data: {e}")
    
    def load_recent_activity(self):
        """Load recent activity data."""
        try:
            activity_query = """
            SELECT 
                datetime(issue_date) as time,
                'فاتورة جديدة: ' || invoice_number as description,
                total as amount
            FROM invoices 
            ORDER BY issue_date DESC 
            LIMIT 10
            """
            activity_data = self.db_manager.execute_query(activity_query)
            
            # Format for display
            formatted_activities = []
            for row in activity_data:
                formatted_activities.append({
                    'time': row['time'],
                    'description': row['description'],
                    'amount': f"{row['amount']:,.0f}"
                })
            
            self.activity_widget.update_activities(formatted_activities)
            
        except Exception as e:
            print(f"Error loading recent activity: {e}")
    
    def on_date_range_changed(self, range_text):
        """Handle date range change."""
        # This would filter data based on selected range
        self.load_dashboard_data()

