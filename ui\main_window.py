#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Main Window for فوترها (Fawterha)
The main application window that contains all views
"""

from PySide6.QtWidgets import (
    QMainWindow, QTabWidget, QVBoxLayout, QHBoxLayout,
    QWidget, QPushButton, QLabel, QStatusBar, QToolBar,
    QMessageBox, QApplication
)
from PySide6.QtCore import Qt, QSize, Signal
from PySide6.QtGui import QIcon, QAction, QPixmap, QFont

from models.currency import Currency
import os
import sqlite3

from ui.error_dialog import show_database_error
from utils.translation_manager import get_translation_manager, tr
from database.language_manager import get_language_manager
from utils.restart_manager import RestartManager

# Import new advanced systems
from utils.performance_manager import get_performance_monitor, PerformanceOptimizer
from utils.notification_system import get_notification_manager, NotificationType, NotificationPriority
from utils.keyboard_shortcuts import get_shortcuts_manager
from utils.advanced_search import AdvancedSearchWidget
# from ui.dashboard_view import DashboardView

from ui.customers_view import CustomersView
from ui.invoices_view import InvoicesView
from ui.reports_view import ReportsView
from ui.settings_view import SettingsView
from ui.products_view import ProductsView
from ui.templates_view import TemplatesView
from ui.currencies_view import CurrenciesView
from ui.inventory_view import InventoryView
from ui.theme_manager import ThemeSelector

# Accounting system views
from ui.general_ledger_view import GeneralLedgerView
from ui.accounts_payable_receivable_view import AccountsPayableReceivableView
from ui.financial_reports_view import FinancialReportsView
from ui.warehouses_view import WarehousesView
from ui.inventory_alerts_view import InventoryAlertsView
from ui.pos_view import POSView
from database.account_manager import AccountManager
from database.transaction_manager import TransactionManager
from database.accounting_period_manager import AccountingPeriodManager
from database.enhanced_inventory_manager import EnhancedInventoryManager


class MainWindow(QMainWindow):
    """Main application window."""

    # Signals to notify when settings are changed
    theme_changed = Signal(str)
    language_changed = Signal(str)

    def __init__(self, db_manager, currency_manager=None, theme_manager=None, language_manager=None):
        """Initialize the main window.

        Args:
            db_manager: Database manager instance
            currency_manager: Currency manager instance
            theme_manager: Theme manager instance
            language_manager: Language manager instance
        """
        super().__init__()

        self.db_manager = db_manager
        self.currency_manager = currency_manager
        self.theme_manager = theme_manager
        self.language_manager = language_manager
        self.translation_manager = get_translation_manager()

        # Initialize advanced systems
        self.performance_monitor = get_performance_monitor(db_manager)
        self.notification_manager = get_notification_manager(db_manager)
        self.shortcuts_manager = get_shortcuts_manager(self)

        # Connect performance alerts
        if self.performance_monitor:
            self.performance_monitor.performance_alert.connect(self.on_performance_alert)

        # Connect notification signals
        if self.notification_manager:
            self.notification_manager.notification_added.connect(self.on_notification_added)

        # We don't need to set a style here as it will be applied by the theme manager

        # Get current language and set direction
        current_language = self.language_manager.get_current_language() if self.language_manager else 'ar'
        is_rtl = self.translation_manager.is_rtl()

        # Set window properties
        self.setWindowTitle("فوترها - Fawterha")
        self.setMinimumSize(1000, 700)

        # Set window to fullscreen mode
        self.showMaximized()

        # Set window direction based on language
        if is_rtl:
            self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        else:
            self.setLayoutDirection(Qt.LayoutDirection.LeftToRight)

        # Create the central widget and layout
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        self.main_layout = QVBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(10, 10, 10, 10)
        self.main_layout.setSpacing(5)

        # Create the toolbar
        self.create_toolbar()

        # Create advanced search widget
        self.advanced_search = AdvancedSearchWidget(self.db_manager)
        self.advanced_search.result_selected.connect(self.on_search_result_selected)
        self.advanced_search.setVisible(False)  # Hidden by default
        self.main_layout.addWidget(self.advanced_search)

        # Create the tab widget
        self.tab_widget = QTabWidget()
        self.tab_widget.currentChanged.connect(self.on_tab_changed)
        self.main_layout.addWidget(self.tab_widget)

        # Create the status bar (will be styled by the theme)
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # Initialize the tabs
        self.init_tabs()

        # Setup keyboard shortcuts
        self.setup_keyboard_shortcuts()

        # Apply database optimizations
        self.apply_database_optimizations()

        # Show a welcome message
        self.status_bar.showMessage(tr("dashboard.welcome", "مرحباً بك في تطبيق فوترها"))

        # Show welcome notification
        if self.notification_manager:
            self.notification_manager.show_notification(
                title=tr("app.welcome", "مرحباً"),
                message=tr("app.welcome_message", "مرحباً بك في النسخة المحسنة من فوترها!"),
                type=NotificationType.SUCCESS,
                expires_in_minutes=5
            )

    def create_toolbar(self):
        """Create the main toolbar."""
        self.toolbar = QToolBar(tr("menu.main_toolbar", "Main Toolbar"))
        self.toolbar.setIconSize(QSize(32, 32))

        # The toolbar will be styled by the theme system
        # We'll set a unique object name to target it in CSS
        self.toolbar.setObjectName("mainToolBar")
        self.addToolBar(self.toolbar)

        # Add new invoice action
        new_invoice_action = QAction(tr("invoices.new_invoice", "فاتورة جديدة"), self)
        new_invoice_action.setStatusTip(tr("invoices.create_invoice", "إنشاء فاتورة جديدة"))
        new_invoice_action.triggered.connect(self.create_new_invoice)
        self.toolbar.addAction(new_invoice_action)

        # Add new customer action
        new_customer_action = QAction(tr("customers.new_customer", "عميل جديد"), self)
        new_customer_action.setStatusTip(tr("customers.create_customer", "إضافة عميل جديد"))
        new_customer_action.triggered.connect(self.create_new_customer)
        self.toolbar.addAction(new_customer_action)

        # Add new product action
        new_product_action = QAction(tr("products.new_product", "منتج/خدمة جديدة"), self)
        new_product_action.setStatusTip(tr("products.create_product", "إضافة منتج أو خدمة جديدة"))
        new_product_action.triggered.connect(self.create_new_product)
        self.toolbar.addAction(new_product_action)

        # Add separator
        self.toolbar.addSeparator()

        # Add theme selector if theme manager is available
        if self.theme_manager:
            # Create theme selector widget
            self.theme_selector = ThemeSelector(self.theme_manager)

            # Add theme selector to toolbar
            self.toolbar.addWidget(self.theme_selector)

            # Connect theme selector's theme_changed signal to our on_theme_changed method
            self.theme_selector.theme_changed.connect(self.on_theme_changed)

            # Add separator
            self.toolbar.addSeparator()

        # Add POS action
        pos_action = QAction(tr("pos.system_name", "نظام نقاط البيع"), self)
        pos_action.setStatusTip(tr("pos.system_name", "الوصول إلى نظام نقاط البيع"))
        pos_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(13))  # POS tab index
        self.toolbar.addAction(pos_action)

        # Add settings action
        settings_action = QAction(tr("settings.title", "الإعدادات"), self)
        settings_action.setStatusTip(tr("settings.save_settings", "تعديل إعدادات التطبيق"))
        settings_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(14))  # Updated index for settings tab
        self.toolbar.addAction(settings_action)

        # Add search action
        search_action = QAction(tr("menu.search", "بحث متقدم"), self)
        search_action.setStatusTip(tr("menu.search", "البحث المتقدم في البيانات"))
        search_action.triggered.connect(self.toggle_advanced_search)
        self.toolbar.addAction(search_action)

        # Add notifications action
        notifications_action = QAction(tr("menu.notifications", "الإشعارات"), self)
        notifications_action.setStatusTip(tr("menu.notifications", "عرض مركز الإشعارات"))
        notifications_action.triggered.connect(self.show_notification_center)
        self.toolbar.addAction(notifications_action)

        # Add about action
        about_action = QAction(tr("menu.about", "حول"), self)
        about_action.setStatusTip(tr("menu.about", "معلومات عن التطبيق"))
        about_action.triggered.connect(self.show_about_dialog)
        self.toolbar.addAction(about_action)

    def apply_translations(self):
        """Apply translations to all UI elements."""
        # Set window title
        self.setWindowTitle(tr("app_name", "فوترها"))

        # Set window direction based on language
        is_rtl = self.translation_manager.is_rtl()
        if is_rtl:
            self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        else:
            self.setLayoutDirection(Qt.LayoutDirection.LeftToRight)

        # Update tab titles
        if hasattr(self, 'tab_widget'):
            # Only update if tabs are initialized
            tab_count = self.tab_widget.count()
            if tab_count > 0:
                pass  # تم إزالة لوحة التحكم
            if tab_count > 1:
                self.tab_widget.setTabText(0, tr("menu.invoices", "الفواتير"))
            if tab_count > 2:
                self.tab_widget.setTabText(1, tr("menu.customers", "العملاء"))
            if tab_count > 3:
                self.tab_widget.setTabText(2, tr("products.title", "المنتجات والخدمات"))
            if tab_count > 4:
                self.tab_widget.setTabText(3, tr("inventory.title", "إدارة المخزون"))
            if tab_count > 5:
                self.tab_widget.setTabText(4, tr("inventory.warehouses", "المستودعات"))
            if tab_count > 6:
                self.tab_widget.setTabText(5, tr("inventory.alerts", "تنبيهات المخزون"))
            if tab_count > 7:
                self.tab_widget.setTabText(6, tr("invoices.title", "قوالب الفواتير"))
            if tab_count > 8:
                self.tab_widget.setTabText(7, tr("reports.title", "التقارير"))
            if tab_count > 9:
                self.tab_widget.setTabText(8, tr("currency.title", "العملات"))
            if tab_count > 10:
                self.tab_widget.setTabText(9, tr("accounting.general_ledger", "دفتر الأستاذ العام"))
            if tab_count > 11:
                self.tab_widget.setTabText(10, tr("accounting.accounts_payable_receivable", "الذمم الدائنة والمدينة"))
            if tab_count > 12:
                self.tab_widget.setTabText(11, tr("accounting.financial_reports", "التقارير المالية"))
            if tab_count > 13:
                self.tab_widget.setTabText(12, tr("pos.system_name", "نظام نقاط البيع"))
            if tab_count > 14:
                self.tab_widget.setTabText(13, tr("settings.title", "الإعدادات"))

        # Update toolbar actions
        for action in self.toolbar.actions():
            if action.text() == "فاتورة جديدة":
                action.setText(tr("invoices.new_invoice", "فاتورة جديدة"))
                action.setStatusTip(tr("invoices.create_invoice", "إنشاء فاتورة جديدة"))
            elif action.text() == "عميل جديد":
                action.setText(tr("customers.new_customer", "عميل جديد"))
                action.setStatusTip(tr("customers.create_customer", "إضافة عميل جديد"))
            elif action.text() == "منتج/خدمة جديدة":
                action.setText(tr("products.new_product", "منتج/خدمة جديدة"))
                action.setStatusTip(tr("products.create_product", "إضافة منتج أو خدمة جديدة"))
            elif action.text() == "الإعدادات":
                action.setText(tr("settings.title", "الإعدادات"))
                action.setStatusTip(tr("settings.save_settings", "تعديل إعدادات التطبيق"))
            elif action.text() == "حول":
                action.setText(tr("menu.about", "حول"))
                action.setStatusTip(tr("menu.about", "معلومات عن التطبيق"))

    def init_tabs(self):
        """Initialize the tab widgets."""
        # Dashboard tab (new advanced dashboard)
        # تم إزالة لوحة التحكم

        # Invoices tab
        self.invoices_view = InvoicesView(self.db_manager, self.currency_manager, self.theme_manager)
        self.tab_widget.addTab(self.invoices_view, tr("menu.invoices", "الفواتير"))

        # Connect invoice updated signal to refresh reports
        self.invoices_view.invoice_updated.connect(self.on_invoice_updated)

        # Customers tab
        self.customers_view = CustomersView(self.db_manager, self.currency_manager)
        self.tab_widget.addTab(self.customers_view, tr("menu.customers", "العملاء"))

        # Products tab
        self.products_view = ProductsView(self.db_manager, False, self.currency_manager)
        self.tab_widget.addTab(self.products_view, tr("products.title", "المنتجات والخدمات"))

        # Inventory tab
        self.inventory_view = InventoryView(self.db_manager, self.currency_manager)
        self.tab_widget.addTab(self.inventory_view, tr("inventory.title", "إدارة المخزون"))

        # Warehouses tab
        self.warehouses_view = WarehousesView(self.db_manager)
        self.tab_widget.addTab(self.warehouses_view, tr("inventory.warehouses", "المستودعات"))

        # Inventory Alerts tab
        self.inventory_alerts_view = InventoryAlertsView(self.db_manager)
        self.tab_widget.addTab(self.inventory_alerts_view, tr("inventory.alerts", "تنبيهات المخزون"))

        # Templates tab
        self.templates_view = TemplatesView(self.db_manager)
        self.tab_widget.addTab(self.templates_view, tr("invoices.title", "قوالب الفواتير"))

        # Reports tab
        self.reports_view = ReportsView(self.db_manager, self.currency_manager)
        self.tab_widget.addTab(self.reports_view, tr("reports.title", "التقارير"))

        # Currencies tab (if currency manager is available)
        if self.currency_manager:
            self.currencies_view = CurrenciesView(self.db_manager, self.currency_manager)
            self.tab_widget.addTab(self.currencies_view, tr("currency.title", "العملات"))

            # Connect currency changed signal
            self.currencies_view.currency_changed.connect(self.on_currency_changed)

        # Accounting tabs
        # General Ledger tab
        self.general_ledger_view = GeneralLedgerView(self.db_manager, self.currency_manager)
        self.tab_widget.addTab(self.general_ledger_view, tr("accounting.general_ledger", "دفتر الأستاذ العام"))

        # Accounts Payable and Receivable tab
        self.accounts_pr_view = AccountsPayableReceivableView(self.db_manager, self.currency_manager)
        self.tab_widget.addTab(self.accounts_pr_view, tr("accounting.accounts_payable_receivable", "الذمم الدائنة والمدينة"))

        # Financial Reports tab
        self.financial_reports_view = FinancialReportsView(self.db_manager, self.currency_manager)
        self.tab_widget.addTab(self.financial_reports_view, tr("accounting.financial_reports", "التقارير المالية"))

        # POS tab
        self.pos_view = POSView(self.db_manager, self.currency_manager)
        self.tab_widget.addTab(self.pos_view, tr("pos.system_name", "نظام نقاط البيع"))

        # Settings tab
        self.settings_view = SettingsView(self.db_manager, self.theme_manager)
        self.tab_widget.addTab(self.settings_view, tr("settings.title", "الإعدادات"))

        # Connect theme changed signal
        self.settings_view.theme_changed.connect(self.on_theme_changed)

        # Connect language changed signal
        self.settings_view.language_changed.connect(self.on_language_changed)


        # ربط الإجراءات السريعة في لوحة التحكم
        if hasattr(self, 'dashboard_view'):
            self.dashboard_view.create_new_invoice = self.create_new_invoice
            self.dashboard_view.create_new_customer = lambda: self.tab_widget.setCurrentIndex(2)  # العملاء
            self.dashboard_view.create_new_product = lambda: self.tab_widget.setCurrentIndex(3)   # المنتجات
            self.dashboard_view.open_reports = lambda: self.tab_widget.setCurrentIndex(8)         # التقارير
            self.dashboard_view.open_pos = lambda: self.tab_widget.setCurrentIndex(13)            # نقطة البيع
            self.dashboard_view.open_settings = lambda: self.tab_widget.setCurrentIndex(14)       # الإعدادات

        # Apply translations to all UI elements
        self.apply_translations()

    def create_new_invoice(self):
        """Create a new invoice."""
        self.tab_widget.setCurrentIndex(0)  # Switch to invoices tab
        self.invoices_view.create_new_invoice()

    def create_new_customer(self):
        """Create a new customer."""
        self.tab_widget.setCurrentIndex(2)  # Switch to customers tab
        self.customers_view.create_new_customer()

    def create_new_product(self):
        """Create a new product or service."""
        self.tab_widget.setCurrentIndex(3)  # Switch to products tab
        self.products_view.create_new_product()

    def on_theme_changed(self, theme_key):
        """Handle theme change event with improved error handling.

        Args:
            theme_key (str): Key of the selected theme
        """
        # Save theme setting to database
        try:
            # Use the execute_update method instead of direct connection
            update_query = "UPDATE settings SET value = ?, updated_at = CURRENT_TIMESTAMP WHERE key = ?"
            self.db_manager.execute_update(update_query, (theme_key, 'theme'))

            # Emit theme changed signal
            self.theme_changed.emit(theme_key)

            # Show success message
            self.status_bar.showMessage(tr("settings.theme_changed", "تم تغيير النمط بنجاح"), 3000)
        except sqlite3.Error as e:
            error_msg = str(e)
            print(f"Database error when changing theme: {error_msg}")

            # Check if it's a closed database error
            if "database is locked" in error_msg.lower() or "closed database" in error_msg.lower():
                def repair_database():
                    """Repair the database and retry the operation."""
                    if self.db_manager.repair_database():
                        QApplication.processEvents()
                        # Try again after repair
                        try:
                            update_query = "UPDATE settings SET value = ?, updated_at = CURRENT_TIMESTAMP WHERE key = ?"
                            self.db_manager.execute_update(update_query, (theme_key, 'theme'))
                            self.theme_changed.emit(theme_key)
                            self.status_bar.showMessage(tr("settings.database_repaired_theme_changed", "تم إصلاح قاعدة البيانات وتغيير النمط بنجاح"), 3000)
                        except Exception as retry_error:
                            self.status_bar.showMessage(tr("settings.theme_change_failed_after_repair", f"فشل تغيير النمط بعد الإصلاح: {str(retry_error)}"), 5000)

                def retry_operation():
                    """Retry the operation without repairing."""
                    try:
                        update_query = "UPDATE settings SET value = ?, updated_at = CURRENT_TIMESTAMP WHERE key = ?"
                        self.db_manager.execute_update(update_query, (theme_key, 'theme'))
                        self.theme_changed.emit(theme_key)
                        self.status_bar.showMessage(tr("settings.theme_changed_after_retry", "تم تغيير النمط بنجاح بعد إعادة المحاولة"), 3000)
                    except Exception as retry_error:
                        self.status_bar.showMessage(tr("settings.theme_change_failed_after_retry", f"فشل تغيير النمط بعد إعادة المحاولة: {str(retry_error)}"), 5000)

                # Show the database error dialog
                show_database_error(
                    self,
                    tr("errors.database_error_during_theme_change", "حدث خطأ في قاعدة البيانات أثناء تغيير النمط"),
                    error_msg,
                    on_repair=repair_database,
                    on_retry=retry_operation
                )
            else:
                # For other database errors, just show a message
                self.status_bar.showMessage(tr("settings.theme_save_error", f"حدث خطأ أثناء حفظ النمط: {error_msg}"), 5000)
        except Exception as e:
            # For non-database errors
            self.status_bar.showMessage(tr("settings.theme_change_error", f"حدث خطأ أثناء تغيير النمط: {str(e)}"), 5000)

    def on_currency_changed(self, currency_code):
        """Handle currency change event.

        Args:
            currency_code (str): Code of the selected currency
        """
        try:
            # Get the currency by code
            if self.currency_manager:
                currency = self.currency_manager.get_currency_by_code(currency_code)

                if currency:
                    # Show success message
                    self.status_bar.showMessage(tr("currency.primary_currency_changed", f"تم تغيير العملة الرئيسية إلى {currency.name}"), 3000)

                    # Update all UI elements that display currency
                    self.update_currency_display(currency)
                else:
                    self.status_bar.showMessage(tr("currency.currency_not_found", f"العملة غير موجودة: {currency_code}"), 3000)
        except Exception as e:
            # For errors
            self.status_bar.showMessage(tr("currency.currency_change_error", f"حدث خطأ أثناء تغيير العملة: {str(e)}"), 5000)
            print(f"Error handling currency change: {str(e)}")

    def update_currency_display(self, currency):
        """Update all UI elements that display currency.

        Args:
            currency (Currency): The new primary currency
        """
        try:
            # Update settings
            conn = self.db_manager.connect()
            cursor = conn.cursor()
            cursor.execute(
                "UPDATE settings SET value = ?, updated_at = CURRENT_TIMESTAMP WHERE key = ?",
                (currency.code, 'currency')
            )
            conn.commit()
            self.db_manager.close()

            # Refresh all tabs that display currency
            if hasattr(self, 'invoices_view'):
                # Refresh invoices view
                if hasattr(self.invoices_view, 'load_invoices'):
                    self.invoices_view.load_invoices()

            if hasattr(self, 'reports_view'):
                # Refresh reports view
                if hasattr(self.reports_view, 'refresh_reports'):
                    print("Refreshing reports view after currency change")
                    self.reports_view.refresh_reports()

                    # Connect to currency_updated signal if not already connected
                    try:
                        self.reports_view.currency_updated.connect(self.on_reports_currency_updated)
                    except:
                        # Signal might already be connected
                        pass

            if hasattr(self, 'products_view'):
                # Refresh products view
                if hasattr(self.products_view, 'load_products'):
                    self.products_view.load_products()
                if hasattr(self.products_view, 'load_services'):
                    self.products_view.load_services()

            if hasattr(self, 'inventory_view'):
                # Refresh inventory view
                if hasattr(self.inventory_view, 'load_data'):
                    self.inventory_view.load_data()

            if hasattr(self, 'warehouses_view'):
                # Refresh warehouses view
                if hasattr(self.warehouses_view, 'load_data'):
                    self.warehouses_view.load_data()

            if hasattr(self, 'inventory_alerts_view'):
                # Refresh inventory alerts view
                if hasattr(self.inventory_alerts_view, 'load_data'):
                    self.inventory_alerts_view.load_data()

            # Show enhanced message about currency change with restart option
            RestartManager.show_restart_confirmation(
                self,
                tr("currency.primary_currency_change_title", "تغيير العملة الرئيسية"),
                tr("messages.currency_change_with_restart", "تم تغيير العملة الرئيسية بنجاح.\n\nهل تريد إعادة تشغيل التطبيق الآن لتطبيق جميع التغييرات؟")
            )
        except Exception as e:
            print(f"Error updating currency display: {str(e)}")

    def on_reports_currency_updated(self):
        """Handle currency updated signal from reports view."""
        print("Reports currency updated signal received")
        # Refresh other views that might need updating
        if hasattr(self, 'invoices_view'):
            if hasattr(self.invoices_view, 'load_invoices'):
                self.invoices_view.load_invoices()

        if hasattr(self, 'products_view'):
            if hasattr(self.products_view, 'load_products'):
                self.products_view.load_products()
            if hasattr(self.products_view, 'load_services'):
                self.products_view.load_services()

        if hasattr(self, 'inventory_view'):
            if hasattr(self.inventory_view, 'load_data'):
                self.inventory_view.load_data()

        if hasattr(self, 'warehouses_view'):
            if hasattr(self.warehouses_view, 'load_data'):
                self.warehouses_view.load_data()

        if hasattr(self, 'inventory_alerts_view'):
            if hasattr(self.inventory_alerts_view, 'load_data'):
                self.inventory_alerts_view.load_data()

    def on_language_changed(self, language_code):
        """Handle language change event.

        Args:
            language_code (str): Code of the selected language
        """
        # Flag to prevent infinite loop
        self._is_changing_language = getattr(self, '_is_changing_language', False)

        # If already changing language, return
        if self._is_changing_language:
            return

        try:
            # Set flag to prevent infinite loop
            self._is_changing_language = True

            # Save language setting to database
            update_query = "UPDATE settings SET value = ?, updated_at = CURRENT_TIMESTAMP WHERE key = ?"
            self.db_manager.execute_update(update_query, (language_code, 'language'))

            # Set language in language manager without triggering signals
            if self.language_manager:
                # Disconnect translation manager signal temporarily
                translation_manager = get_translation_manager()
                try:
                    translation_manager.language_changed.disconnect()
                except:
                    pass  # Signal might not be connected

                # Set language without emitting signal
                self.language_manager.set_language(language_code, emit_signal=False)

                # Reconnect signal to main window
                try:
                    translation_manager.language_changed.connect(self.on_language_changed)
                except:
                    pass  # Signal might already be connected

            # Apply translations to UI
            self.apply_translations()

            # Emit language changed signal
            self.language_changed.emit(language_code)

            # Show success message
            self.status_bar.showMessage(tr("messages.changes_saved", "تم تغيير اللغة بنجاح. سيتم تطبيق التغييرات عند إعادة تشغيل التطبيق"), 5000)

            # Show enhanced message about language change with restart option
            RestartManager.show_restart_confirmation(
                self,
                tr("settings.language", "تغيير اللغة"),
                tr("messages.language_change_with_restart", "تم تغيير لغة التطبيق بنجاح.\n\nهل تريد إعادة تشغيل التطبيق الآن لتطبيق التغييرات؟")
            )
        except sqlite3.Error as e:
            error_msg = str(e)
            print(f"Database error when changing language: {error_msg}")

            # For database errors, just show a message
            self.status_bar.showMessage(f"حدث خطأ أثناء حفظ اللغة: {error_msg}", 5000)
        except Exception as e:
            # For non-database errors
            self.status_bar.showMessage(f"حدث خطأ أثناء تغيير اللغة: {str(e)}", 5000)
        finally:
            # Reset flag
            self._is_changing_language = False

    def on_invoice_updated(self):
        """Handle invoice updated signal from invoices view."""
        print("Invoice updated signal received - refreshing reports")
        # Refresh reports view if it exists
        if hasattr(self, 'reports_view'):
            if hasattr(self.reports_view, 'refresh_reports'):
                self.reports_view.refresh_reports()

        # Also refresh inventory views if they exist
        if hasattr(self, 'inventory_view'):
            if hasattr(self.inventory_view, 'load_data'):
                self.inventory_view.load_data()

        if hasattr(self, 'warehouses_view'):
            if hasattr(self.warehouses_view, 'load_data'):
                self.warehouses_view.load_data()

        if hasattr(self, 'inventory_alerts_view'):
            if hasattr(self.inventory_alerts_view, 'check_for_alerts'):
                self.inventory_alerts_view.check_for_alerts()

    def on_tab_changed(self, index):
        """Handle tab change event.

        Args:
            index (int): Index of the selected tab
        """
        # Check if the selected tab is the reports tab
        if hasattr(self, 'reports_view') and self.tab_widget.widget(index) == self.reports_view:
            print("Switched to reports tab - refreshing reports")
            # Refresh reports when switching to the reports tab
            if hasattr(self.reports_view, 'refresh_reports'):
                self.reports_view.refresh_reports()

    def show_about_dialog(self):
        """Show the about dialog with improved contrast."""
        about_box = QMessageBox(self)
        about_box.setObjectName("aboutDialog")  # Set object name for styling
        about_box.setWindowTitle(tr("menu.about_app", "حول فوترها"))

        # Apply custom styling to the dialog for better contrast
        about_box.setStyleSheet("""
            QMessageBox {
                background-color: white;
                border: 1px solid #bdbdbd;
                border-radius: 8px;
            }
            QLabel {
                color: #212121;
                font-size: 12pt;
            }
            QMessageBox QLabel#qt_msgbox_label {
                min-width: 400px;
            }
        """)

        # Set icon
        about_box.setIconPixmap(QPixmap("resources/icons/logo.png").scaled(80, 80) if os.path.exists("resources/icons/logo.png") else QPixmap())

        # Set text with improved styling for better contrast
        about_text = f"""
        <div style="text-align: center; background-color: #f5f5f5; padding: 20px; border-radius: 8px;">
            <h1 style="color: #0d47a1; font-size: 28px; margin-bottom: 15px; text-shadow: 1px 1px 2px rgba(0,0,0,0.1);">{tr("app_name", "فوترها - Fawterha")}</h1>
            <p style="color: #212121; font-size: 18px; margin: 8px 0; font-weight: bold;">{tr("menu.version", "الإصدار 1.0")}</p>
            <p style="color: #212121; font-size: 16px; margin: 8px 0; line-height: 1.5;">{tr("menu.app_description", "تطبيق إدارة الفواتير للمشاريع الصغيرة والشركات")}</p>
            <p style="color: #0d47a1; font-size: 16px; margin: 15px 0 5px 0; font-weight: bold;">{tr("menu.copyright", "حقوق الملكية © Hadou Design")}</p>
        </div>
        """
        about_box.setText(about_text)

        # Add OK button with custom styling
        ok_button = about_box.addButton(tr("common.ok", "موافق"), QMessageBox.AcceptRole)
        # Button will be styled by the global theme
        ok_button.setMinimumWidth(120)
        ok_button.setMinimumHeight(40)
        ok_button.setFont(QFont("Arial", 12, QFont.Bold))

        # Set dialog size
        about_box.setMinimumWidth(500)

        # Show the dialog
        about_box.exec()

    def setup_keyboard_shortcuts(self):
        """Setup keyboard shortcuts for the application."""
        if self.shortcuts_manager:
            self.shortcuts_manager.connect_main_window_shortcuts(self)
            print("Keyboard shortcuts initialized")

    def apply_database_optimizations(self):
        """Apply database performance optimizations."""
        try:
            PerformanceOptimizer.optimize_database_settings(self.db_manager)
            print("Database optimizations applied")
        except Exception as e:
            print(f"Error applying database optimizations: {e}")

    def toggle_advanced_search(self):
        """Toggle the advanced search widget visibility."""
        if self.advanced_search.isVisible():
            self.advanced_search.setVisible(False)
            self.status_bar.showMessage(tr("search.hidden", "تم إخفاء البحث المتقدم"), 2000)
        else:
            self.advanced_search.setVisible(True)
            self.advanced_search.search_input.setFocus()
            self.status_bar.showMessage(tr("search.shown", "تم عرض البحث المتقدم"), 2000)

    def show_notification_center(self):
        """Show the notification center."""
        if self.notification_manager:
            notification_center = self.notification_manager.get_notification_center()
            notification_center.show()
            notification_center.raise_()
            notification_center.activateWindow()

    def on_search_result_selected(self, result):
        """Handle search result selection."""
        try:
            if result.type == "invoice":
                # Switch to invoices tab and select the invoice
                self.tab_widget.setCurrentIndex(1)  # Invoices tab (index 1)
                if hasattr(self.invoices_view, 'select_invoice'):
                    self.invoices_view.select_invoice(result.id)

            elif result.type == "customer":
                # Switch to customers tab and select the customer
                self.tab_widget.setCurrentIndex(2)  # Customers tab (index 2)
                if hasattr(self.customers_view, 'select_customer'):
                    self.customers_view.select_customer(result.id)

            elif result.type == "product":
                # Switch to products tab and select the product
                self.tab_widget.setCurrentIndex(3)  # Products tab (index 3)
                if hasattr(self.products_view, 'select_product'):
                    self.products_view.select_product(result.id)

            # Hide search widget after selection
            self.advanced_search.setVisible(False)

            self.status_bar.showMessage(
                tr("search.result_selected", "تم تحديد: {0}").format(result.title), 3000
            )

        except Exception as e:
            print(f"Error handling search result selection: {e}")

    def on_performance_alert(self, alert_type: str, message: str):
        """Handle performance alerts."""
        if self.notification_manager:
            priority = NotificationPriority.HIGH if "high" in alert_type else NotificationPriority.MEDIUM

            self.notification_manager.show_notification(
                title=tr("performance.alert", "تنبيه الأداء"),
                message=message,
                type=NotificationType.WARNING,
                priority=priority,
                persistent=True,
                action_text=tr("performance.optimize", "تحسين الأداء"),
                action_callback=self.optimize_performance
            )

    def on_notification_added(self, notification):
        """Handle new notification added."""
        # Show in status bar for important notifications
        if notification.priority.value >= NotificationPriority.HIGH.value:
            self.status_bar.showMessage(
                f"🔔 {notification.title}: {notification.message}", 5000
            )

    def optimize_performance(self):
        """Optimize application performance."""
        try:
            if self.performance_monitor:
                self.performance_monitor.optimize_performance()

            # Apply database optimizations
            self.apply_database_optimizations()

            # Show success notification
            if self.notification_manager:
                self.notification_manager.show_notification(
                    title=tr("performance.optimized", "تم التحسين"),
                    message=tr("performance.optimization_complete", "تم تحسين أداء التطبيق بنجاح"),
                    type=NotificationType.SUCCESS,
                    expires_in_minutes=3
                )

            self.status_bar.showMessage(tr("performance.optimized", "تم تحسين الأداء"), 3000)

        except Exception as e:
            print(f"Error optimizing performance: {e}")
            if self.notification_manager:
                self.notification_manager.show_notification(
                    title=tr("performance.optimization_failed", "فشل التحسين"),
                    message=tr("performance.optimization_error", "حدث خطأ أثناء تحسين الأداء"),
                    type=NotificationType.ERROR
                )

    def closeEvent(self, event):
        """Handle application close event."""
        try:
            # Cleanup performance monitor
            if self.performance_monitor:
                self.performance_monitor.cleanup()

            # Cleanup shortcuts
            if self.shortcuts_manager:
                self.shortcuts_manager.cleanup()

            # Close database connections
            if self.db_manager:
                self.db_manager.close()

            print("Application cleanup completed")

        except Exception as e:
            print(f"Error during application cleanup: {e}")

        # Accept the close event
        event.accept()
